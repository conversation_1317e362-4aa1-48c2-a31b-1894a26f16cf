<!-- UserDepartment.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import { PlusOutline, MinusOutline, TrashBinSolid, EditOutline } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
    import { invalidateAll } from '$app/navigation';
    import { Accordion, AccordionItem, Indicator } from 'flowbite-svelte';
    import { onMount, tick } from 'svelte';

    export let departmentNames = [];
    import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

    let isAddingDepartment = false;
    let departmentFormErrors: string | null = null;
    let departmentToDelete: number | null = null;
    let departmentToEdit: number | null = null;
    let isSubmittingDepartment = false;

    // Shared state for color picker
    let colorPickerOpen = false;
    let activePickerId: string | null = null;
    
    // For new department
    let newDepartmentColor = colorOptions[0]?.name || 'gray';
    
    // For editing
    let selectedColor = colorOptions[0]?.name || 'gray';

    onMount(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
          colorPickerOpen = false;
          activePickerId = null;
        }
      };
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    });

    async function toggleColorPicker(id: string) {
      if (activePickerId === id && colorPickerOpen) {
        colorPickerOpen = false;
        activePickerId = null;
      } else {
        activePickerId = id;
        await tick();
        colorPickerOpen = true;
      }
    }

    function chooseColor(name: string) {
      if (departmentToEdit !== null) {
        selectedColor = name;
      } else {
        newDepartmentColor = name;
      }
      colorPickerOpen = false;
      activePickerId = null;
    }

    function handleDepartmentSubmit() {
        isSubmittingDepartment = true;
        departmentFormErrors = null;

        return async ({ result, update }) => {
            isSubmittingDepartment = false;

            if (result.type === 'success') {
                const form = document.querySelector('form') as HTMLFormElement;
                form?.reset();
                isAddingDepartment = false;
                departmentToEdit = null;
                await invalidateAll();
            } else if (result.type === 'failure') {
                departmentFormErrors = result.data?.error || 'An unexpected error occurred';
            }
        };
    }

    function confirmDepartmentDelete(deptId: number) {
        departmentToDelete = deptId;
    }

    function cancelDepartmentDelete() {
        departmentToDelete = null;
    }

    function startDepartmentEdit(department) {
        departmentToEdit = department.id;
        selectedColor = department.color || 'gray';
    }

    function cancelDepartmentEdit() {
        departmentToEdit = null;
        departmentFormErrors = null;
        colorPickerOpen = false;
        activePickerId = null;
    }
</script>


<AccordionItem>
    <span slot="header" class="flex flex-col w-full">
        <h2 class="text-xl font-medium text-gray-700">{t('departments')}</h2>
        <p class="text-sm text-gray-500">{t('departments_desc')}</p>
    </span>
    <div class="space-y-4">
        
        {#if departmentNames.length > 0}
            <ul class="space-y-2">
                {#each departmentNames as department (department.id)}
                    <li class="px-4 py-2 rounded-lg flex justify-between items-center">
                        {#if departmentToEdit === department.id}
                            <form 
                                method="POST" 
                                action="?/update_department"
                                use:enhance={handleDepartmentSubmit}
                                class="w-full space-y-4"
                            >
                                <input 
                                    type="hidden" 
                                    name="backend_department_id" 
                                    value={department.id} 
                                />
                                <input 
                                    type="hidden" 
                                    name="color" 
                                    value={selectedColor} 
                                />
                                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                    <!-- Added Color Picker for Edit mode -->
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">
                                            {t('select_color')}
                                        </label>
                                        <div class="relative">
                                            <button
                                                type="button"
                                                class="flex items-center px-3 py-2 border border-gray-300 rounded-lg"
                                                on:click|stopPropagation={() => toggleColorPicker(`edit-dept-${department.id}`)}
                                            >
                                                <!-- <Indicator size="lg" color={selectedColor} class="mr-2" /> -->
                                                <Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
                                                <span>{selectedColor}</span>
                                            </button>
                                            {#if activePickerId === `edit-dept-${department.id}` && colorPickerOpen}
                                                <div class="absolute top-full left-0 mt-2 z-20 p-3 bg-white rounded-lg shadow-lg color-picker-area" style="min-width: 170px;">
                                                    <div class="grid grid-cols-6 gap-3">
                                                        {#each colorOptions as opt}
                                                        <button
                                                            type="button"
                                                            class={`w-6 h-6 rounded-full cursor-pointer ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
                                                            on:click|stopPropagation={() => chooseColor(opt.name)}
                                                            aria-label={`Select ${opt.name} color`}
                                                        ></button>
                                                        {/each}
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="dept_name" class="block text-sm font-medium text-gray-700">
                                            {t('name')}
                                        </label>
                                        <input
                                            type="text"
                                            id="dept_name"
                                            name="name"
                                            required
                                            value={department.name}
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Enter department name..."
                                        />
                                    </div>
                                    <div class="space-y-2">
                                        <label for="dept_code" class="block text-sm font-medium text-gray-700">
                                            {t('code')}
                                        </label>
                                        <input
                                            type="text"
                                            id="dept_code"
                                            name="code"
                                            required
                                            value={department.code}
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Enter department code..."
                                        />
                                    </div>
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="dept_description" class="block text-sm font-medium text-gray-700">
                                        {t('description')}
                                    </label>
                                    <textarea
                                        id="dept_description"
                                        name="description"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Enter department description..."
                                    >{department.description}</textarea>
                                </div>
                                <input
                                    type="hidden" 
                                    id="dept_active"
                                    name="is_active"
                                    value="true"
                                    checked={department.is_active}
                                    class="h-5 w-5"
                                />
                                {#if departmentFormErrors}
                                    <div class="bg-red-100 text-red-700 p-3 rounded-lg">
                                        {departmentFormErrors}
                                    </div>
                                {/if}
                                <div class="flex space-x-2 mt-4">
                                    <button 
                                        type="button"
                                        on:click={cancelDepartmentEdit}
                                        class="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                                    >
                                        
                                        {t('cancel')}
                                    </button>
                                    <button 
                                        type="submit" 
                                        disabled={isSubmittingDepartment}
                                        class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                                    >
                                        {isSubmittingDepartment ? t('updating') : t('update')}
                                    </button>
                                </div>
                            </form>
                        {:else}
                            <!-- Department Info Section -->
                            <div class="space-y-2 flex-grow">
                                <div class="flex items-center gap-3">
                                    <!-- <Indicator size="lg" color={department.color ?? 'gray'} class="mr-2" /> -->
                                    <Indicator size="lg" class={`${getColorClass(department.color)} mr-2`} />
                                    <h3 class="font-medium text-gray-900 text-lg">{department.name} ({department.code})</h3>
                                </div>
                                
                                {#if department.description}
                                    <div class="text-sm text-gray-700 mt-2">
                                        <div>
                                            <span class="block text-gray-500 mb-1">{t('description')}: </span>
                                            <p class="border-gray-200">{department.description}</p>
                                        </div>
                                    </div>
                                {/if}
                            </div>

                        
                            <div class="flex items-center space-x-2">
                                <button 
                                    on:click={() => startDepartmentEdit(department)}
                                    class="text-gray-400 hover:text-gray-800 mr-2"
                                >
                                    <EditOutline class="w-5 h-5" />
                                </button>
                                {#if departmentToDelete === department.id}
                                    <div class="flex space-x-2">
                                        <form 
                                            method="POST" 
                                            action="?/delete_department"
                                            use:enhance={handleDepartmentSubmit}
                                            class="flex items-center"
                                        >
                                            <input 
                                                type="hidden" 
                                                name="backend_department_id" 
                                                value={department.id} 
                                            />
                                            <button 
                                                type="submit" 
                                                disabled={isSubmittingDepartment}
                                                class="text-red-600 hover:text-red-800 text-sm hover:underline"
                                            >
                                                {t('delete')}
                                            </button>
                                        </form>
                                        <button 
                                            on:click={cancelDepartmentDelete} 
                                            class="text-gray-500 hover:text-gray-800 text-sm hover:underline"
                                        >
                                            {t('cancel')}
                                        </button>
                                    </div>
                                {:else}
                                    <button 
                                        on:click={() => confirmDepartmentDelete(department.id)}
                                        class="text-red-500 hover:text-red-700"
                                    >
                                        <TrashBinSolid class="w-5 h-5" />
                                    </button>
                                {/if}
                            </div>
                        {/if}
                    </li>

                    <!-- Divider line between sections -->
                   <hr class="my-6 border-t border-gray-300">
                {/each}
            </ul>
        {:else if !isAddingDepartment}
            <p class="text-gray-500 italic text-center">{t('no_departments')}</p>
        {/if}


        <form 
            method="POST" 
            action="?/create_new_department_action"
            use:enhance={handleDepartmentSubmit}
            class="space-y-4 mt-4"
        >

            <!-- <div class="flex justify-start"> -->
            <div class="flex space-x-2 mt-4">
                <button 
                    on:click={() => {
                        isAddingDepartment = !isAddingDepartment;
                        departmentFormErrors = null;
                    }}
                    class="text-gray-700 px-4 py-2 rounded-lg focus:outline-none border hover:bg-gray-100"
                >
                    {#if isAddingDepartment}
                        {t('cancel')}
                    {:else}
                        <div class="flex justify-center items-center">
                            <PlusOutline class="inline-block w-5 h-5 mr-1" />
                            <span>{t('add_department')}</span>
                        </div>
                    {/if}
                </button>

                {#if isAddingDepartment}
                    <div class="flex space-x-2">
                        <button 
                            type="submit" 
                            disabled={isSubmittingDepartment}
                            class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                        >
                            {isSubmittingDepartment ? t('creating') : t('confirm')}
                        </button>
                    </div>
                {/if}
            </div>

            {#if isAddingDepartment}
                {#if departmentFormErrors}
                    <div class="bg-red-100 text-red-700 p-3 rounded-lg">
                        {departmentFormErrors}
                    </div>
                {/if}
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">
                            {t('select_color')}
                        </label>
                        <div class="relative">
                            <button
                                type="button"
                                class="flex items-center px-3 py-2 border border-gray-300 rounded-lg"
                                on:click|stopPropagation={() => toggleColorPicker('new-dept')}
                            >
                                <!-- <Indicator size="lg" color={newDepartmentColor} class="mr-2" /> -->
                                <Indicator size="lg" class={`mr-1 ${getColorClass(newDepartmentColor)}`} />
                                <span>{newDepartmentColor}</span>
                            </button>
                            <input type="hidden" name="color" value={newDepartmentColor} />
                            {#if activePickerId === 'new-dept' && colorPickerOpen}
                                <div class="absolute top-full left-0 mt-2 z-20 p-3 bg-white rounded-lg shadow-lg color-picker-area" style="min-width: 170px;">
                                    <div class="grid grid-cols-6 gap-3">
                                        {#each colorOptions as opt}
                                        <button
                                            type="button"
                                            class={`w-6 h-6 rounded-full cursor-pointer ${opt.class} border ${newDepartmentColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
                                            on:click|stopPropagation={() => chooseColor(opt.name)}
                                            aria-label={`Select ${opt.name} color`}
                                        ></button>
                                        {/each}
                                    </div>
                                </div>
                            {/if}
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label for="new_dept_name" class="block text-sm font-medium text-gray-700">
                            {t('name')}
                        </label>
                        <input
                            type="text"
                            id="new_dept_name"
                            name="name"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder={t('name_desc')}
                        />
                    </div>

                    <div class="space-y-2">
                        <label for="new_dept_code" class="block text-sm font-medium text-gray-700">
                            {t('code')}
                        </label>
                        <input
                            type="text"
                            id="new_dept_code"
                            name="code"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder={t('code_desc')}
                        />
                    </div>
                </div>
                
                <div class="space-y-2">
                    <label for="new_dept_description" class="block text-sm font-medium text-gray-700">
                        {t('description')}
                    </label>
                    <textarea
                        id="new_dept_description"
                        name="description"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={t('description_desc')}
                    ></textarea>
                </div>
                <input
                    type="hidden"
                    id="new_dept_active"
                    name="is_active"
                    value="true"
                    checked
                    class="h-5 w-5"
                />
            {/if}
        </form>
    </div>
</AccordionItem>