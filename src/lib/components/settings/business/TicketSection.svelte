<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import { onD<PERSON>roy, onMount, createEventDispatcher } from 'svelte';
    import { enhance } from '$app/forms';
    import { fly } from 'svelte/transition';
    import { Toast, Timeline, TimelineItem, Button, Toggle } from 'flowbite-svelte';
    import {
        CloseCircleSolid,
        CheckCircleSolid
    } from 'flowbite-svelte-icons';
    
    export let transferSettings;
    
    // Create the dispatch function for events
    const dispatch = createEventDispatcher();

    // State variables for transfer settings
    let transferChangedFields = new Set();
    let hasUnsavedTransferChanges = false;
    
    // Helper function to convert any value to proper boolean
    function toBoolean(value) {
        // Handle both boolean and string representations
        return value === true || value === 'True' || value === 'true';
    }
    
    // Store initial values
    let originalTransferValues = {};
    
    // Initialize when component mounts
    onMount(() => {
        // Initialize original values on component mount
        initializeValues();
        
        // Listen for tab changes
        window.addEventListener('tab-changed', handleTabChange);
        
        return () => {
            window.removeEventListener('tab-changed', handleTabChange);
        };
    });
    
    // Handle tab changes - reset state if coming back to this tab
    function handleTabChange(event) {
        if (event.detail && event.detail.currentTab === 'transfer') {
            console.log("Returned to transfer tab - reinitializing");
            initializeValues();
        }
    }
    
    // Separate initialization function for reuse
    function initializeValues() {
        originalTransferValues = {
            transfer_ticket_partner: toBoolean($transferSettings.transfer_ticket_partner),
            transfer_ticket_department: toBoolean($transferSettings.transfer_ticket_department),
            transfer_ticket_tag: toBoolean($transferSettings.transfer_ticket_tag)
        };
        
        transferChangedFields.clear();
        hasUnsavedTransferChanges = false;
        
        console.log("Transfer settings initialized:", originalTransferValues);
    }

    // Toast state
    let toastStatus = false;
    let toastMessage = "Changes saved successfully!";
    let counter = 0;
    let settingsForm;

    // Update the trackTransferChange function with improved change detection
    function trackTransferChange(field, value) {
        console.log(`Tracking change for ${field}: ${value}, original was: ${originalTransferValues[field]}`);
        
        // Update transferSettings store correctly
        transferSettings.update(settings => {
            return { ...settings, [field]: value };
        });
        
        // Track changes by comparing with original values
        const currentValue = toBoolean($transferSettings[field]);
        const originalValue = toBoolean(originalTransferValues[field]);
        
        console.log(`Comparing: current=${currentValue}, original=${originalValue}`);
        
        if (currentValue !== originalValue) {
            transferChangedFields.add(field);
        } else {
            transferChangedFields.delete(field);
        }
        
        // Update the unsaved changes flag based on set size
        hasUnsavedTransferChanges = transferChangedFields.size > 0;
        console.log(`Has unsaved changes: ${hasUnsavedTransferChanges}, Fields: ${Array.from(transferChangedFields).join(', ')}`);
    }

    // Ensure proper saving of values with explicit boolean conversion
    function saveTransferSettings() {
        console.log("Saving transfer settings");
        
        // Always include ALL settings in the submission, not just changed ones
        const settings = [
            { key: "AUTO_TRANSFER_TICKET_PARTNER_CONDITION", value: toBoolean($transferSettings.transfer_ticket_partner) ? "True" : "False" },
            { key: "AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION", value: toBoolean($transferSettings.transfer_ticket_department) ? "True" : "False" },
            { key: "AUTO_TRANSFER_TICKET_USER_TAG_CONDITION", value: toBoolean($transferSettings.transfer_ticket_tag) ? "True" : "False" }
        ];
        
        console.log("Settings to save:", settings);

        // Make sure settingsForm is properly defined
        const settingsInput = settingsForm.querySelector('input[name="settings"]');
        settingsInput.value = JSON.stringify(settings);
        
        toastMessage = "Transfer settings saved successfully!";
        toastStatus = true;
        counter = 4;
        timeout();
        
        settingsForm.requestSubmit();
    }

    // Toast timeout method
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }

    // Refresh the component state
    function refreshScreen() {
        console.log("Refreshing screen");
        
        // Reset change tracking
        transferChangedFields.clear();
        hasUnsavedTransferChanges = false;
        
        // Update original values to match current values
        originalTransferValues = {
            transfer_ticket_partner: toBoolean($transferSettings.transfer_ticket_partner),
            transfer_ticket_department: toBoolean($transferSettings.transfer_ticket_department),
            transfer_ticket_tag: toBoolean($transferSettings.transfer_ticket_tag)
        };
        
        console.log("Updated original values:", originalTransferValues);
    }

    // Handle form submission with enhanced feedback
    function handleFormSubmit() {
        return {
            update({ form, data, action, result }) {
                console.log("Form submission completed", result);
                
                // Perform local refresh
                refreshScreen();
                
                // Dispatch event to parent to notify of update
                dispatch('settings-updated');
                
                // Also trigger a global event for other components that might need to know
                window.dispatchEvent(new CustomEvent('settings-updated'));
                
                // Show success notification
                toastMessage = "Transfer settings saved successfully!";
                toastStatus = true;
                counter = 4;
                timeout();
            }
        };
    }
    
    // Fix for the toggle controls - improved string-boolean conversion
    $: transferDepartmentChecked = toBoolean($transferSettings.transfer_ticket_department);
    $: transferPartnerChecked = toBoolean($transferSettings.transfer_ticket_partner);
    $: transferTagChecked = toBoolean($transferSettings.transfer_ticket_tag);
    
    // Force a reassessment of changes whenever transferSettings store updates
    $: {
        if ($transferSettings) {
            // Recalculate which fields have changed
            transferChangedFields.clear();
            
            if (originalTransferValues.transfer_ticket_partner !== undefined && 
                toBoolean($transferSettings.transfer_ticket_partner) !== toBoolean(originalTransferValues.transfer_ticket_partner)) {
                transferChangedFields.add('transfer_ticket_partner');
            }
            
            if (originalTransferValues.transfer_ticket_department !== undefined && 
                toBoolean($transferSettings.transfer_ticket_department) !== toBoolean(originalTransferValues.transfer_ticket_department)) {
                transferChangedFields.add('transfer_ticket_department');
            }
            
            if (originalTransferValues.transfer_ticket_tag !== undefined && 
                toBoolean($transferSettings.transfer_ticket_tag) !== toBoolean(originalTransferValues.transfer_ticket_tag)) {
                transferChangedFields.add('transfer_ticket_tag');
            }
            
            hasUnsavedTransferChanges = transferChangedFields.size > 0;
        }
    }
</script>

<div class="space-y-4 relative">
    <!-- Success toast notification using Flowbite Svelte Toast -->
    {#if toastStatus}
        <Toast
            color="green"
            transition={fly}
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}

    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-medium text-gray-700">{t('ticket_transfer_settings')}</h2>
            <p class="text-sm text-gray-500">{t('ticket_transfer_description')}</p>
        </div>
    
        <button 
            type="button" 
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm 
                {hasUnsavedTransferChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
            on:click={saveTransferSettings}
            disabled={!hasUnsavedTransferChanges}
        >
            {t('save_changes')}
        </button>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Workflow Timeline -->
        <Timeline>
            <TimelineItem title={t('step_1_title')} date="Step 1">
                <div class="border border-gray-200 rounded-lg p-3 bg-amber-50">
                    <p class="text-base font-normal text-gray-600">
                        {t('step_1_desc')} <span class="text-xs text-green-600 font-normal">({t('default')})</span>
                    </p>
                    <p class="text-sm text-gray-500">{t('step_1_desc_remark')}</p>
                </div>
            </TimelineItem>
            
            <TimelineItem title={t('step_2_title')} date={`Step 2 (${t('optional')})`}>
                <p class="text-base font-normal text-gray-600">
                    {t('step_2_desc')}
                </p>
                <div class="mt-3 flex items-center">
                    <!-- Fixed toggle with proper accessibility -->
                    <label id="dept-toggle-label" class="flex items-center cursor-pointer">
                        <Toggle 
                            id="dept-toggle"
                            color="green"
                            checked={transferDepartmentChecked}
                            on:change={(e) => {
                                const newValue = e.target.checked;
                                trackTransferChange('transfer_ticket_department', newValue);
                            }}
                            aria-labelledby="dept-toggle-label"
                        />
                        <span class="ml-2 text-sm font-medium text-gray-700">
                            {t('enable_department')}
                            {#if transferChangedFields.has('transfer_ticket_department')}
                                <span class="text-blue-600 ml-1">{t('modified')}</span>
                            {/if}
                        </span>
                    </label>
                </div>
            </TimelineItem>

            <!-- <TimelineItem title="Partner Matching" date={`Step 3 (${t('optional')})`}>
                <p class="text-base font-normal text-gray-600">
                    Match tickets to preferred partner agents.
                </p>
                <div class="mt-3 flex items-center">
                    <label id="partner-toggle-label" class="flex items-center cursor-pointer">
                        <Toggle 
                            id="partner-toggle"
                            checked={transferPartnerChecked}
                            on:change={(e) => {
                                const newValue = e.target.checked;
                                trackTransferChange('transfer_ticket_partner', newValue);
                            }}
                            aria-labelledby="partner-toggle-label"
                        />
                        <span class="ml-2 text-sm font-medium text-gray-700">
                            Enable Partner 
                            {#if transferChangedFields.has('transfer_ticket_partner')}
                                <span class="text-blue-600 ml-1">{t('modified')}</span>
                            {/if}
                        </span>
                    </label>
                </div>
            </TimelineItem> -->
                    
            <TimelineItem title={t('step_3_title')} date="Step 3">
                <div class="border border-gray-200 rounded-lg p-3 bg-amber-50"> 
                    <p class="text-base font-normal text-gray-600">
                        {t('step_3_desc')}  <span class="text-xs text-green-600 font-normal">({t('default')})</span>
                    </p>
                </div>
            </TimelineItem>
            
            <TimelineItem title={t('step_4_title')} date={`Step 4 (${t('optional')})`}>
                <p class="text-base font-normal text-gray-600">
                    {t('step_4_desc')}
                </p>
                <div class="mt-3 flex items-center">
                    <!-- Fixed toggle with explicit event handling -->
                    <label id="tag-toggle-label" class="flex items-center cursor-pointer">
                        <Toggle 
                            id="tag-toggle"
                            color="green"
                            checked={transferTagChecked}
                            on:change={(e) => {
                                const newValue = e.target.checked;
                                trackTransferChange('transfer_ticket_tag', newValue);
                            }}
                            aria-labelledby="tag-toggle-label"
                        />
                        <span class="ml-2 text-sm font-medium text-gray-700">
                            {t('enable_tag')}
                            {#if transferChangedFields.has('transfer_ticket_tag')}
                                <span class="text-blue-600 ml-1">{t('modified')}</span>
                            {/if}
                        </span>
                    </label>
                </div>
            </TimelineItem>
                    
            <TimelineItem title={t('step_5_title')} date="Step 5">
                <div class="grid grid-cols-1 gap-3">
                    <div class="border border-gray-200 rounded-lg p-3 bg-amber-50">
                        <p class="text-base font-medium text-gray-700">
                            {t('algorithm_frequent')}<span class="text-xs text-green-600 font-normal">({t('default')})</span>
                        </p>
                        <p class="text-sm text-gray-500">{t('algorithm_frequent_desc')}</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-3 bg-amber-50">
                        <p class="text-base font-medium text-gray-700">{t('algorithm_csat')} 
                            <span class="text-xs text-green-600 font-normal">({t('default')})</span>
                        </p>
                        <p class="text-sm text-gray-500">{t('algorithm_csat_desc')}</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-3 bg-amber-50">
                        <div class="flex items-center justify-between">
                            <p class="text-base font-medium text-gray-700">
                                {t('algorithm_workload')} <span class="text-xs text-green-600 font-normal">({t('default')})</span>
                            </p>
                        </div>
                        <p class="text-sm text-gray-500">{t('algorithm_workload_desc')}</p>
                    </div>
                </div>
            </TimelineItem>
        </Timeline>
    </div>
    
    {#if hasUnsavedTransferChanges}
        <div class="bg-amber-50 border-l-4 border-amber-400 p-4 mt-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-amber-700">
                        You have unsaved changes to your transfer settings.
                    </p>
                </div>
            </div>
        </div>
    {/if}

    <!-- Hidden form for transfer settings submission - FIXED ENHANCE FUNCTION -->
    <form 
        bind:this={settingsForm} 
        action="?/update_system_setting" 
        method="POST" 
        use:enhance={handleFormSubmit}
        class="hidden"
    >
        <input type="hidden" name="settings" value="" />
    </form>
</div>